class UserStats {
  final int currentStreak;
  final int longestStreak;
  final int totalWorkouts;
  final int totalMinutes;
  final int totalCaloriesBurned;
  final int weeklyGoal;
  final Map<String, MonthlyStats> monthlyStats;
  final String? lastWorkoutDate;

  UserStats({
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.totalWorkouts = 0,
    this.totalMinutes = 0,
    this.totalCaloriesBurned = 0,
    this.weeklyGoal = 150,
    this.monthlyStats = const {},
    this.lastWorkoutDate,
  });

  factory UserStats.fromMap(Map<String, dynamic> map) {
    final monthlyStatsMap = <String, MonthlyStats>{};
    final monthlyData = map['monthlyStats'] as Map<String, dynamic>? ?? {};
    
    for (final entry in monthlyData.entries) {
      monthlyStatsMap[entry.key] = MonthlyStats.fromMap(
        entry.value as Map<String, dynamic>
      );
    }

    return UserStats(
      currentStreak: map['currentStreak'] ?? 0,
      longestStreak: map['longestStreak'] ?? 0,
      totalWorkouts: map['totalWorkouts'] ?? 0,
      totalMinutes: map['totalMinutes'] ?? 0,
      totalCaloriesBurned: map['totalCaloriesBurned'] ?? 0,
      weeklyGoal: map['weeklyGoal'] ?? 150,
      monthlyStats: monthlyStatsMap,
      lastWorkoutDate: map['lastWorkoutDate'],
    );
  }

  Map<String, dynamic> toMap() {
    final monthlyStatsMap = <String, dynamic>{};
    for (final entry in monthlyStats.entries) {
      monthlyStatsMap[entry.key] = entry.value.toMap();
    }

    return {
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'totalWorkouts': totalWorkouts,
      'totalMinutes': totalMinutes,
      'totalCaloriesBurned': totalCaloriesBurned,
      'weeklyGoal': weeklyGoal,
      'monthlyStats': monthlyStatsMap,
      'lastWorkoutDate': lastWorkoutDate,
    };
  }

  // Helper methods
  double get averageWorkoutDuration {
    if (totalWorkouts == 0) return 0;
    return totalMinutes / totalWorkouts;
  }

  double get averageCaloriesPerWorkout {
    if (totalWorkouts == 0) return 0;
    return totalCaloriesBurned / totalWorkouts;
  }

  MonthlyStats? getMonthlyStats(String yearMonth) {
    return monthlyStats[yearMonth];
  }

  MonthlyStats getCurrentMonthStats() {
    final now = DateTime.now();
    final currentMonth = '${now.year}-${now.month.toString().padLeft(2, '0')}';
    return monthlyStats[currentMonth] ?? MonthlyStats();
  }
}

class MonthlyStats {
  final int workouts;
  final int minutes;
  final int calories;

  MonthlyStats({
    this.workouts = 0,
    this.minutes = 0,
    this.calories = 0,
  });

  factory MonthlyStats.fromMap(Map<String, dynamic> map) {
    return MonthlyStats(
      workouts: map['workouts'] ?? 0,
      minutes: map['minutes'] ?? 0,
      calories: map['calories'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'workouts': workouts,
      'minutes': minutes,
      'calories': calories,
    };
  }

  MonthlyStats copyWith({
    int? workouts,
    int? minutes,
    int? calories,
  }) {
    return MonthlyStats(
      workouts: workouts ?? this.workouts,
      minutes: minutes ?? this.minutes,
      calories: calories ?? this.calories,
    );
  }
}
