import 'package:cloud_firestore/cloud_firestore.dart';

class WorkoutModel {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final WorkoutType type;
  final int duration; // in minutes
  final int caloriesBurned;
  final DateTime date;
  final DateTime createdAt;
  final List<ExerciseSet> exercises;
  final String? notes;

  WorkoutModel({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    required this.type,
    required this.duration,
    required this.caloriesBurned,
    required this.date,
    required this.createdAt,
    this.exercises = const [],
    this.notes,
  });

  factory WorkoutModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkoutModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      name: data['name'] ?? '',
      description: data['description'],
      type: WorkoutType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => WorkoutType.other,
      ),
      duration: data['duration'] ?? 0,
      caloriesBurned: data['caloriesBurned'] ?? 0,
      date: (data['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      exercises: (data['exercises'] as List<dynamic>?)
              ?.map((e) => ExerciseSet.fromMap(e as Map<String, dynamic>))
              .toList() ??
          [],
      notes: data['notes'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'name': name,
      'description': description,
      'type': type.name,
      'duration': duration,
      'caloriesBurned': caloriesBurned,
      'date': Timestamp.fromDate(date),
      'createdAt': Timestamp.fromDate(createdAt),
      'exercises': exercises.map((e) => e.toMap()).toList(),
      'notes': notes,
    };
  }
}

enum WorkoutType {
  cardio,
  strength,
  flexibility,
  sports,
  other,
}

class ExerciseSet {
  final String name;
  final int? reps;
  final double? weight; // in kg
  final int? duration; // in seconds
  final double? distance; // in meters

  ExerciseSet({
    required this.name,
    this.reps,
    this.weight,
    this.duration,
    this.distance,
  });

  factory ExerciseSet.fromMap(Map<String, dynamic> map) {
    return ExerciseSet(
      name: map['name'] ?? '',
      reps: map['reps'],
      weight: map['weight']?.toDouble(),
      duration: map['duration'],
      distance: map['distance']?.toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'reps': reps,
      'weight': weight,
      'duration': duration,
      'distance': distance,
    };
  }
}
