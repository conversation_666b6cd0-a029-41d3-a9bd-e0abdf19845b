import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String email;
  final String name;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? profilePictureUrl;
  final DateTime? dateOfBirth;
  final double? height; // in cm
  final double? weight; // in kg
  final List<String> fitnessGoals;
  final String activityLevel;

  UserModel({
    required this.uid,
    required this.email,
    required this.name,
    required this.createdAt,
    required this.updatedAt,
    this.profilePictureUrl,
    this.dateOfBirth,
    this.height,
    this.weight,
    this.fitnessGoals = const [],
    this.activityLevel = 'moderate',
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      uid: doc.id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      profilePictureUrl: data['profilePictureUrl'],
      dateOfBirth: (data['dateOfBirth'] as Timestamp?)?.toDate(),
      height: data['height']?.toDouble(),
      weight: data['weight']?.toDouble(),
      fitnessGoals: List<String>.from(data['fitnessGoals'] ?? []),
      activityLevel: data['activityLevel'] ?? 'moderate',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'profilePictureUrl': profilePictureUrl,
      'dateOfBirth': dateOfBirth != null ? Timestamp.fromDate(dateOfBirth!) : null,
      'height': height,
      'weight': weight,
      'fitnessGoals': fitnessGoals,
      'activityLevel': activityLevel,
    };
  }

  UserModel copyWith({
    String? uid,
    String? email,
    String? name,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? profilePictureUrl,
    DateTime? dateOfBirth,
    double? height,
    double? weight,
    List<String>? fitnessGoals,
    String? activityLevel,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      fitnessGoals: fitnessGoals ?? this.fitnessGoals,
      activityLevel: activityLevel ?? this.activityLevel,
    );
  }

  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  double? get bmi {
    if (height == null || weight == null) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }
}
