import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String email;
  final String displayName;
  final String? photoURL;
  final UserProfile profile;
  final UserFitness fitness;
  final UserPreferences preferences;
  final UserStats stats;
  final bool onboardingCompleted;
  final DateTime? onboardingCompletedAt;
  final int createdAt;
  final DateTime updatedAt;
  final DateTime lastUpdated;

  UserModel({
    required this.uid,
    required this.email,
    required this.displayName,
    this.photoURL,
    required this.profile,
    required this.fitness,
    required this.preferences,
    required this.stats,
    this.onboardingCompleted = false,
    this.onboardingCompletedAt,
    required this.createdAt,
    required this.updatedAt,
    required this.lastUpdated,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      uid: doc.id,
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      photoURL: data['photoURL'],
      profile: UserProfile.fromMap(data['profile'] ?? {}),
      fitness: UserFitness.fromMap(data['fitness'] ?? {}),
      preferences: UserPreferences.fromMap(data['preferences'] ?? {}),
      stats: UserStats.fromMap(data['stats'] ?? {}),
      onboardingCompleted: data['onboardingCompleted'] ?? false,
      onboardingCompletedAt: (data['onboardingCompletedAt'] as Timestamp?)?.toDate(),
      createdAt: data['createdAt'] ?? 0,
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastUpdated: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'profile': profile.toMap(),
      'fitness': fitness.toMap(),
      'preferences': preferences.toMap(),
      'stats': stats.toMap(),
      'onboardingCompleted': onboardingCompleted,
      'onboardingCompletedAt': onboardingCompletedAt != null
          ? Timestamp.fromDate(onboardingCompletedAt!) : null,
      'createdAt': createdAt,
      'updatedAt': Timestamp.fromDate(updatedAt),
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  UserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    UserProfile? profile,
    UserFitness? fitness,
    UserPreferences? preferences,
    UserStats? stats,
    bool? onboardingCompleted,
    DateTime? onboardingCompletedAt,
    int? createdAt,
    DateTime? updatedAt,
    DateTime? lastUpdated,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      profile: profile ?? this.profile,
      fitness: fitness ?? this.fitness,
      preferences: preferences ?? this.preferences,
      stats: stats ?? this.stats,
      onboardingCompleted: onboardingCompleted ?? this.onboardingCompleted,
      onboardingCompletedAt: onboardingCompletedAt ?? this.onboardingCompletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  int? get age {
    if (profile.dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - profile.dateOfBirth!.year;
    if (now.month < profile.dateOfBirth!.month ||
        (now.month == profile.dateOfBirth!.month && now.day < profile.dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  double? get bmi {
    if (profile.height == null || profile.weight == null) return null;
    final heightInMeters = profile.height! / 100;
    return profile.weight! / (heightInMeters * heightInMeters);
  }
}
