class UserPreferences {
  final int workoutsPerWeek;
  final int durationMinutes;
  final List<String> environments;
  final String theme;
  final bool notifications;
  final String language;
  final Map<String, dynamic> appSettings;
  final String units;
  final bool onboardingComplete;
  final bool fitnessGoalsSet;
  final bool comprehensiveOnboardingComplete;

  UserPreferences({
    this.workoutsPerWeek = 3,
    this.durationMinutes = 30,
    this.environments = const ['homeNoEquipment'],
    this.theme = 'system',
    this.notifications = true,
    this.language = 'en',
    this.appSettings = const {},
    this.units = 'metric',
    this.onboardingComplete = false,
    this.fitnessGoalsSet = false,
    this.comprehensiveOnboardingComplete = false,
  });

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      workoutsPerWeek: map['workoutsPerWeek'] ?? 3,
      durationMinutes: map['durationMinutes'] ?? 30,
      environments: List<String>.from(map['environments'] ?? ['homeNoEquipment']),
      theme: map['theme'] ?? 'system',
      notifications: map['notifications'] ?? true,
      language: map['language'] ?? 'en',
      appSettings: Map<String, dynamic>.from(map['appSettings'] ?? {}),
      units: map['units'] ?? 'metric',
      onboardingComplete: map['onboardingComplete'] ?? false,
      fitnessGoalsSet: map['fitnessGoalsSet'] ?? false,
      comprehensiveOnboardingComplete: map['comprehensiveOnboardingComplete'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'workoutsPerWeek': workoutsPerWeek,
      'durationMinutes': durationMinutes,
      'environments': environments,
      'theme': theme,
      'notifications': notifications,
      'language': language,
      'appSettings': appSettings,
      'units': units,
      'onboardingComplete': onboardingComplete,
      'fitnessGoalsSet': fitnessGoalsSet,
      'comprehensiveOnboardingComplete': comprehensiveOnboardingComplete,
    };
  }
}

enum WorkoutEnvironment {
  homeNoEquipment,
  homeWithEquipment,
  gym,
  largeGym,
  outdoor,
  pool,
}

enum ThemeMode {
  system,
  light,
  dark,
}

enum PreferredUnits {
  metric,
  imperial,
}
