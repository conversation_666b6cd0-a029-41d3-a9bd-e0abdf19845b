import 'package:cloud_firestore/cloud_firestore.dart';

class NutritionModel {
  final String id;
  final String userId;
  final DateTime date;
  final List<MealEntry> meals;
  final int totalCalories;
  final double totalProtein; // in grams
  final double totalCarbs; // in grams
  final double totalFat; // in grams
  final double waterIntake; // in liters
  final DateTime createdAt;

  NutritionModel({
    required this.id,
    required this.userId,
    required this.date,
    this.meals = const [],
    required this.totalCalories,
    required this.totalProtein,
    required this.totalCarbs,
    required this.totalFat,
    this.waterIntake = 0.0,
    required this.createdAt,
  });

  factory NutritionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NutritionModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      date: (data['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      meals: (data['meals'] as List<dynamic>?)
              ?.map((e) => MealEntry.fromMap(e as Map<String, dynamic>))
              .toList() ??
          [],
      totalCalories: data['totalCalories'] ?? 0,
      totalProtein: data['totalProtein']?.toDouble() ?? 0.0,
      totalCarbs: data['totalCarbs']?.toDouble() ?? 0.0,
      totalFat: data['totalFat']?.toDouble() ?? 0.0,
      waterIntake: data['waterIntake']?.toDouble() ?? 0.0,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'date': Timestamp.fromDate(date),
      'meals': meals.map((e) => e.toMap()).toList(),
      'totalCalories': totalCalories,
      'totalProtein': totalProtein,
      'totalCarbs': totalCarbs,
      'totalFat': totalFat,
      'waterIntake': waterIntake,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

class MealEntry {
  final String name;
  final MealType type;
  final List<FoodItem> foods;
  final DateTime time;

  MealEntry({
    required this.name,
    required this.type,
    this.foods = const [],
    required this.time,
  });

  factory MealEntry.fromMap(Map<String, dynamic> map) {
    return MealEntry(
      name: map['name'] ?? '',
      type: MealType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MealType.other,
      ),
      foods: (map['foods'] as List<dynamic>?)
              ?.map((e) => FoodItem.fromMap(e as Map<String, dynamic>))
              .toList() ??
          [],
      time: (map['time'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'type': type.name,
      'foods': foods.map((e) => e.toMap()).toList(),
      'time': Timestamp.fromDate(time),
    };
  }

  int get totalCalories => foods.fold(0, (total, food) => total + food.calories);
  double get totalProtein => foods.fold(0.0, (total, food) => total + food.protein);
  double get totalCarbs => foods.fold(0.0, (total, food) => total + food.carbs);
  double get totalFat => foods.fold(0.0, (total, food) => total + food.fat);
}

enum MealType {
  breakfast,
  lunch,
  dinner,
  snack,
  other,
}

class FoodItem {
  final String name;
  final double quantity; // serving size
  final String unit; // e.g., "grams", "cups", "pieces"
  final int calories;
  final double protein; // in grams
  final double carbs; // in grams
  final double fat; // in grams

  FoodItem({
    required this.name,
    required this.quantity,
    required this.unit,
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fat,
  });

  factory FoodItem.fromMap(Map<String, dynamic> map) {
    return FoodItem(
      name: map['name'] ?? '',
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unit: map['unit'] ?? '',
      calories: map['calories'] ?? 0,
      protein: map['protein']?.toDouble() ?? 0.0,
      carbs: map['carbs']?.toDouble() ?? 0.0,
      fat: map['fat']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'quantity': quantity,
      'unit': unit,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
    };
  }
}
