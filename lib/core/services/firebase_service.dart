import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class FirebaseService {
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
  static FirebaseAnalytics get analytics => FirebaseAnalytics.instance;

  static Future<void> initialize() async {
    await Firebase.initializeApp();
  }

  // Auth helpers
  static User? get currentUser => auth.currentUser;
  static bool get isAuthenticated => currentUser != null;
  static String? get currentUserId => currentUser?.uid;

  // Firestore collections
  static CollectionReference get users => firestore.collection('users');
  static CollectionReference get workouts => firestore.collection('workouts');
  static CollectionReference get exercises => firestore.collection('exercises');
  static CollectionReference get nutrition => firestore.collection('nutrition');
  static CollectionReference get meals => firestore.collection('meals');
  static CollectionReference get activities => firestore.collection('activities');

  // User-specific collections
  static CollectionReference userWorkouts(String userId) =>
      users.doc(userId).collection('workouts');
  
  static CollectionReference userMeals(String userId) =>
      users.doc(userId).collection('meals');
  
  static CollectionReference userActivities(String userId) =>
      users.doc(userId).collection('activities');

  // Storage references
  static Reference get profilePictures => storage.ref().child('profile_pictures');
  static Reference get workoutImages => storage.ref().child('workout_images');
  static Reference get mealImages => storage.ref().child('meal_images');
}
