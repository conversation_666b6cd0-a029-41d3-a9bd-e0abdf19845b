import 'package:flutter/material.dart';
import '../widgets/activity_summary_card.dart';
import '../widgets/recent_workouts_list.dart';
import '../widgets/nutrition_summary_card.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverAppBar(
            floating: true,
            title: const Text('OpenFit'),
            centerTitle: false,
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(Icons.settings_outlined),
                onPressed: () {},
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back, User!',
                    style: theme.textTheme.displayMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Track your fitness journey',
                    style: theme.textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 24),
                  const ActivitySummaryCard(),
                  const SizedBox(height: 16),
                  Text(
                    'Recent Workouts',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  const RecentWorkoutsList(),
                  const SizedBox(height: 16),
                  Text(
                    'Nutrition Today',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  const NutritionSummaryCard(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
