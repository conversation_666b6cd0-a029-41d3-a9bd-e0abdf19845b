# OpenFit - Fitness Tracking App

A scalable and maintainable Flutter fitness tracking application with a clean architecture.

## 🏗️ Architecture Overview

This project follows a **feature-based architecture** with clean separation of concerns, making it highly scalable and maintainable as you add new features.

### 📁 Project Structure

```
lib/
├── main.dart                    # App entry point
├── app/
│   └── app.dart                # Main app widget with theme configuration
├── core/                       # Shared core functionality
│   ├── constants/
│   │   └── app_constants.dart  # App-wide constants
│   ├── theme/
│   │   ├── app_theme.dart      # Theme configuration
│   │   ├── colors.dart         # Color palette
│   │   └── text_styles.dart    # Typography definitions
│   └── utils/
│       ├── validators.dart     # Input validation utilities
│       └── formatters.dart     # Data formatting utilities
├── features/                   # Feature-based modules
│   ├── home/
│   │   └── presentation/
│   │       └── pages/
│   │           └── home_page.dart
│   ├── dashboard/
│   │   └── presentation/
│   │       ├── pages/
│   │       │   └── dashboard_page.dart
│   │       └── widgets/
│   │           ├── activity_summary_card.dart
│   │           ├── recent_workouts_list.dart
│   │           └── nutrition_summary_card.dart
│   ├── workouts/
│   ├── nutrition/
│   └── profile/
└── shared/                     # Reusable components
    └── widgets/
        ├── cards/
        │   ├── metric_card.dart
        │   └── workout_card.dart
        └── progress/
            └── nutrient_progress.dart
```

## 🚀 Key Features

- **Feature-based Architecture**: Each feature is self-contained and independent
- **Clean Separation**: Clear separation between UI, business logic, and data layers
- **Reusable Components**: Shared widgets for consistent UI across features
- **Theme System**: Centralized theme management with Material 3 support
- **Utility Functions**: Common validators and formatters for data handling
- **Scalable Structure**: Easy to add new features without affecting existing code

## 🎨 Design System

### Colors
- **Primary**: Green (#4CAF50) - Fitness theme
- **Semantic Colors**: Success, Warning, Error, Info
- **Fitness Specific**: Cardio, Strength, Flexibility, Nutrition colors

### Typography
- Material 3 compliant text styles
- Consistent spacing and sizing
- Optimized for readability

## 🧪 Testing

The project includes:
- Widget tests for UI components
- Unit tests for utilities and business logic
- Integration tests for feature workflows

Run tests with:
```bash
flutter test
```

## 📦 Dependencies

### Core Dependencies
- `flutter`: Flutter SDK
- `cupertino_icons`: iOS-style icons
- `intl`: Internationalization support

### Development Dependencies
- `flutter_test`: Testing framework
- `flutter_lints`: Linting rules

## 🔧 Development Guidelines

### Adding New Features

1. **Create Feature Folder**: Add new feature under `lib/features/`
2. **Follow Structure**: Use the same folder structure as existing features
3. **Shared Components**: Place reusable widgets in `lib/shared/widgets/`
4. **Constants**: Add feature-specific constants to `lib/core/constants/`

### Code Organization

- **Pages**: Main screens for each feature
- **Widgets**: Feature-specific UI components
- **Models**: Data structures (to be added)
- **Repositories**: Data access layer (to be added)
- **Providers/Blocs**: State management (to be added)

### Best Practices

1. **Single Responsibility**: Each file should have a single, clear purpose
2. **Consistent Naming**: Use descriptive names following Dart conventions
3. **Reusability**: Extract common UI patterns into shared widgets
4. **Testing**: Write tests for new features and components
5. **Documentation**: Document complex logic and public APIs

## 🚀 Getting Started

1. **Clone the repository**
2. **Install dependencies**:
   ```bash
   flutter pub get
   ```
3. **Run the app**:
   ```bash
   flutter run
   ```
4. **Run tests**:
   ```bash
   flutter test
   ```
5. **Analyze code**:
   ```bash
   flutter analyze
   ```

## 🔮 Future Enhancements

The current structure is ready for:
- **State Management**: Add Riverpod, Bloc, or Provider
- **Navigation**: Implement go_router for advanced routing
- **API Integration**: Add HTTP client and data repositories
- **Local Storage**: Implement Hive or SQLite for offline data
- **Authentication**: Add user authentication flow
- **Internationalization**: Support multiple languages

## 📱 Current Features

- **Dashboard**: Activity summary, recent workouts, nutrition tracking
- **Navigation**: Bottom navigation between main sections
- **Theme Support**: Light and dark mode with Material 3
- **Responsive Design**: Optimized for different screen sizes

This architecture provides a solid foundation for building a comprehensive fitness tracking application that can scale with your needs.
