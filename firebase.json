{"functions": [{"source": "functions", "codebase": "default", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "openfit", "codebase": "openfit", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "aifit", "codebase": "aifit", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "agent_openfit", "codebase": "agent_openfit", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "firestore": {"database": "(default)", "location": "us-central1", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}}